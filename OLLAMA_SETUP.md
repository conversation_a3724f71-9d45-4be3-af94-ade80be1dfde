# Ollama Integration Setup Guide

This guide explains how to set up and use the Ollama integration for violence description in your video analysis system.

## Overview

The system now includes Ollama integration that provides detailed descriptions of violent scenes when violence is detected in uploaded videos. When a video is classified as violent, the system:

1. Identifies the frame with the highest violence confidence
2. Sends that frame to Ollama's Gemma3 model
3. Receives a detailed 2-sentence security analysis
4. Displays the description in a modal window on the frontend

## Prerequisites

### 1. Install Ollama

Download and install Ollama from: https://ollama.ai/

**Windows:**
```bash
# Download the installer from https://ollama.ai/download/windows
# Run the installer and follow the setup wizard
```

**macOS:**
```bash
# Download from https://ollama.ai/download/mac
# Or install via Homebrew:
brew install ollama
```

**Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. Download the Gemma3 Model

After installing Ollama, download the required model:

```bash
ollama pull gemma3:12b-it-qat
```

This will download the Gemma3 12B model optimized for image analysis tasks.

### 3. Start Ollama Service

Start the Ollama service:

```bash
ollama serve
```

The service will run on `http://localhost:11434` by default.

## Installation Steps

### 1. Install Python Dependencies

Make sure you have the updated requirements:

```bash
cd backend
pip install -r requirements.txt
```

The requirements now include the `requests` library needed for Ollama API calls.

### 2. Test Ollama Integration

Run the test script to verify everything is working:

```bash
cd backend
python test_ollama.py
```

This will:
- Check if Ollama is running
- Verify the Gemma3 model is available
- Test image analysis functionality

### 3. Start the Application

1. **Start Ollama** (if not already running):
   ```bash
   ollama serve
   ```

2. **Start the Backend**:
   ```bash
   cd backend
   python app.py
   ```

3. **Open the Frontend**:
   Open `front end/index.html` in your browser

## How It Works

### Backend Integration

The backend (`app.py`) now includes:

- **Ollama API Configuration**: Connects to `http://localhost:11434/api/chat`
- **Frame Capture**: Tracks the frame with highest violence confidence
- **API Call**: Sends the most violent frame to Ollama for analysis
- **Response Handling**: Returns the description to the frontend

### Frontend Integration

The frontend (`upload.js` and `style.css`) now includes:

- **Modal Window**: Displays violence descriptions in a styled popup
- **Automatic Display**: Shows modal when violence is detected
- **Responsive Design**: Works on mobile and desktop
- **Close Functionality**: Click X, outside modal, or press Escape to close

## Usage

1. **Register/Login** to the application
2. **Upload a video** that contains violence
3. **Wait for processing** - the system will:
   - Analyze the video for violence
   - If violence is detected, capture the most violent frame
   - Send the frame to Ollama for description
   - Display results with the description modal
4. **View the description** in the popup modal that appears automatically

## Configuration

### Ollama Model

You can change the model by modifying the `OLLAMA_MODEL` variable in `backend/app.py`:

```python
OLLAMA_MODEL = "gemma3:12b-it-qat"  # Current model
# OLLAMA_MODEL = "llava:latest"     # Alternative model
```

### API Endpoint

If Ollama is running on a different port or host, update the `OLLAMA_API_URL`:

```python
OLLAMA_API_URL = "http://localhost:11434/api/chat"  # Default
# OLLAMA_API_URL = "http://your-server:11434/api/chat"  # Custom
```

## Troubleshooting

### Common Issues

1. **"Connection refused" error**:
   - Make sure Ollama is running: `ollama serve`
   - Check if the service is on port 11434: `curl http://localhost:11434/api/tags`

2. **"Model not found" error**:
   - Download the model: `ollama pull gemma3:12b-it-qat`
   - Check available models: `ollama list`

3. **Slow response times**:
   - The Gemma3 model is large and may take 10-30 seconds for analysis
   - Consider using a smaller model like `llava:7b` for faster responses

4. **Modal not appearing**:
   - Check browser console for JavaScript errors
   - Ensure violence is actually detected in the video
   - Verify the backend is returning the `violence_description` field

### Testing

Run the test script to diagnose issues:

```bash
cd backend
python test_ollama.py
```

### Logs

Check the backend logs for detailed error messages:
- Look for "Ollama" related log messages
- Check for API call success/failure messages
- Verify frame capture and processing logs

## Performance Notes

- **First Request**: May take longer as the model loads into memory
- **Subsequent Requests**: Should be faster once the model is loaded
- **Memory Usage**: The Gemma3 model requires significant RAM (8GB+ recommended)
- **Processing Time**: Expect 10-30 seconds for image analysis

## Security Considerations

- Ollama runs locally, so no data is sent to external servers
- Images are processed on your local machine
- The API uses base64 encoding for image transmission
- No persistent storage of analyzed images

## Next Steps

After setting up Ollama integration, you can:

1. Test with various violent video samples
2. Customize the analysis prompt for your specific needs
3. Integrate with the real-time detection system
4. Add logging and monitoring for production use

For questions or issues, refer to the Ollama documentation: https://ollama.ai/docs
