#!/usr/bin/env python3
"""
Test the Ollama function in isolation
"""

import cv2
import numpy as np
import requests
import base64

# Ollama API configuration
OLLAMA_API_URL = "http://localhost:11434/api/chat"
OLLAMA_MODEL = "gemma3"

def get_violence_description_from_ollama(frame):
    """
    Send frame to Ollama API for violence description using streaming API
    """
    try:
        # Convert frame to base64
        _, buffer = cv2.imencode('.jpg', frame)
        image_base64 = base64.b64encode(buffer).decode('utf-8')

        # Prepare the request payload exactly as specified
        payload = {
            "model": OLLAMA_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": "You're a security analyst. Look at this image and write a short 2-sentence report about any signs of violence, aggression, or suspicious activity — include how many people are there and what they're doing.",
                    "images": [image_base64]
                }
            ]
        }

        # Send the HTTP POST request with streaming enabled
        response = requests.post(OLLAMA_API_URL, json=payload, stream=True, timeout=60)

        # Check the response status
        if response.status_code == 200:
            print("Streaming response from Ollama:")
            full_response = ''
            for line in response.iter_lines(decode_unicode=True):
                if line:  # Ignore empty lines
                    try:
                        # Parse each line as a JSON object
                        json_data = json.loads(line)
                        # Extract and accumulate the assistant's message content
                        if "message" in json_data and "content" in json_data["message"]:
                            content = json_data["message"]["content"]
                            print(content, end="")
                            full_response += content
                    except json.JSONDecodeError:
                        print(f"\nFailed to parse line: {line}")

            print()  # Ensure the final output ends with a newline
            return full_response.strip() if full_response.strip() else "Unable to generate description"
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return "Unable to generate description due to API error"

    except Exception as e:
        print(f"Error calling Ollama API: {str(e)}")
        return "Unable to generate description due to connection error"

def test_function():
    """Test the function with a dummy frame"""
    # Create a test frame
    frame = np.zeros((400, 600, 3), dtype=np.uint8)
    cv2.rectangle(frame, (50, 100), (150, 300), (0, 255, 0), -1)
    cv2.putText(frame, "Test Frame", (200, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    print("Testing Ollama function...")
    result = get_violence_description_from_ollama(frame)
    print(f"Result: {result}")

if __name__ == "__main__":
    test_function()
