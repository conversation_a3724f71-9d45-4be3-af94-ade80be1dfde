#!/usr/bin/env python3
"""
Download Ollama model using Python requests
"""

import requests
import json
import time

def download_model(model_name="llava:latest"):
    """Download a model using Ollama API"""
    url = "http://localhost:11434/api/pull"
    payload = {"name": model_name}
    
    print(f"Downloading model: {model_name}")
    print("This may take several minutes...")
    
    try:
        response = requests.post(url, json=payload, stream=True, timeout=300)
        
        if response.status_code == 200:
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'status' in data:
                            print(f"Status: {data['status']}")
                        if 'completed' in data and 'total' in data:
                            progress = (data['completed'] / data['total']) * 100
                            print(f"Progress: {progress:.1f}%")
                    except json.JSONDecodeError:
                        print(line.decode('utf-8'))
            
            print(f"✅ Model {model_name} downloaded successfully!")
            return True
        else:
            print(f"❌ Error downloading model: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def list_models():
    """List available models"""
    try:
        response = requests.get("http://localhost:11434/api/tags")
        if response.status_code == 200:
            models = response.json()
            print("Available models:")
            for model in models.get('models', []):
                print(f"  - {model['name']}")
            return models.get('models', [])
        else:
            print(f"Error listing models: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error: {str(e)}")
        return []

if __name__ == "__main__":
    print("🚀 Ollama Model Downloader")
    print("=" * 30)
    
    # List current models
    print("Current models:")
    models = list_models()
    
    # Check if llava is already available
    model_names = [model['name'] for model in models]
    if "llava:latest" in model_names:
        print("✅ llava:latest is already available!")
    else:
        print("📥 Downloading llava:latest...")
        download_model("llava:latest")
    
    print("\n📋 Final model list:")
    list_models()
