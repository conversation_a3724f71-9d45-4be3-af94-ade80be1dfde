#!/usr/bin/env python3
"""
Install llava model using Ollama API
"""

import requests
import json
import time

def install_llava():
    """Install llava model using Ollama API"""
    url = "http://localhost:11434/api/pull"
    payload = {"name": "llava"}
    
    print("🚀 Installing llava model...")
    print("This may take several minutes depending on your internet connection...")
    
    try:
        response = requests.post(url, json=payload, stream=True, timeout=600)
        
        if response.status_code == 200:
            print("📥 Downloading llava model...")
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'status' in data:
                            status = data['status']
                            if 'completed' in data and 'total' in data and data['total'] > 0:
                                progress = (data['completed'] / data['total']) * 100
                                print(f"\r{status}: {progress:.1f}%", end="", flush=True)
                            else:
                                print(f"\r{status}", end="", flush=True)
                    except json.JSONDecodeError:
                        continue
            
            print("\n✅ llava model installed successfully!")
            return True
        else:
            print(f"❌ Error installing model: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def check_models():
    """Check what models are available"""
    try:
        response = requests.get("http://localhost:11434/api/tags")
        if response.status_code == 200:
            models = response.json()
            print("\n📋 Available models:")
            for model in models.get('models', []):
                print(f"  ✅ {model['name']}")
            return models.get('models', [])
        else:
            print(f"❌ Error checking models: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return []

if __name__ == "__main__":
    print("🔧 Ollama Model Installer")
    print("=" * 30)
    
    # Check current models
    print("Checking current models...")
    models = check_models()
    
    # Check if llava is already installed
    model_names = [model['name'] for model in models]
    if any('llava' in name for name in model_names):
        print("✅ llava model is already installed!")
    else:
        print("📥 Installing llava model...")
        if install_llava():
            print("\n🎉 Installation complete!")
        else:
            print("\n❌ Installation failed!")
    
    # Final check
    print("\n" + "=" * 30)
    check_models()
