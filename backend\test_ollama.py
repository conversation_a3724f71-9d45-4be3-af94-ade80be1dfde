#!/usr/bin/env python3
"""
Test script to verify Ollama API integration
"""

import requests
import json
import base64
import cv2
import numpy as np

# Ollama API configuration
OLLAMA_API_URL = "http://localhost:11434/api/chat"
OLLAMA_MODEL = "gemma3"

def test_ollama_connection():
    """Test if Ollama is running and accessible"""
    try:
        # Test basic connection
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama is running!")
            print(f"Available models: {[model['name'] for model in models.get('models', [])]}")
            
            # Check if our target model is available
            model_names = [model['name'] for model in models.get('models', [])]
            if OLLAMA_MODEL in model_names:
                print(f"✅ Target model '{OLLAMA_MODEL}' is available!")
                return True
            else:
                print(f"❌ Target model '{OLLAMA_MODEL}' is not available.")
                print("Available models:", model_names)
                return False
        else:
            print(f"❌ Ollama connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error connecting to Ollama: {str(e)}")
        print("Make sure Ollama is running on localhost:11434")
        return False

def create_test_image():
    """Create a simple test image"""
    # Create a simple test image with some shapes
    img = np.zeros((400, 600, 3), dtype=np.uint8)
    
    # Add some colored rectangles to simulate people/objects
    cv2.rectangle(img, (50, 100), (150, 300), (0, 255, 0), -1)  # Green rectangle
    cv2.rectangle(img, (200, 150), (300, 350), (255, 0, 0), -1)  # Blue rectangle
    cv2.rectangle(img, (400, 80), (500, 280), (0, 0, 255), -1)   # Red rectangle
    
    # Add some text
    cv2.putText(img, "Test Violence Scene", (150, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    return img

def test_ollama_image_analysis():
    """Test Ollama image analysis functionality"""
    print("\n🧪 Testing Ollama image analysis...")
    
    # Create test image
    test_image = create_test_image()
    
    # Convert to base64
    _, buffer = cv2.imencode('.jpg', test_image)
    image_base64 = base64.b64encode(buffer).decode('utf-8')
    
    # Prepare the request payload exactly as specified
    payload = {
        "model": OLLAMA_MODEL,
        "messages": [
            {
                "role": "user",
                "content": "You're a security analyst. Look at this image and write a short 2-sentence report about any signs of violence, aggression, or suspicious activity — include how many people are there and what they're doing.",
                "images": [image_base64]
            }
        ]
    }

    try:
        print("Sending request to Ollama...")
        # Send the HTTP POST request with streaming enabled
        response = requests.post(OLLAMA_API_URL, json=payload, stream=True, timeout=60)

        # Check the response status
        if response.status_code == 200:
            print("✅ Ollama analysis successful!")
            print("📝 Streaming response from Ollama:")
            full_response = ''
            for line in response.iter_lines(decode_unicode=True):
                if line:  # Ignore empty lines
                    try:
                        # Parse each line as a JSON object
                        json_data = json.loads(line)
                        # Extract and accumulate the assistant's message content
                        if "message" in json_data and "content" in json_data["message"]:
                            content = json_data["message"]["content"]
                            print(content, end="")
                            full_response += content
                    except json.JSONDecodeError:
                        print(f"\nFailed to parse line: {line}")

            print()  # Ensure the final output ends with a newline
            print(f"📝 Complete analysis result: {full_response}")
            return True
        else:
            print(f"❌ Ollama API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling Ollama API: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Ollama Integration for Violence Detection")
    print("=" * 50)
    
    # Test 1: Connection
    if not test_ollama_connection():
        print("\n❌ Ollama connection test failed. Please ensure:")
        print("1. Ollama is installed and running")
        print("2. The gemma3:12b-it-qat model is downloaded")
        print("3. Ollama is accessible on localhost:11434")
        return False
    
    # Test 2: Image analysis
    if not test_ollama_image_analysis():
        print("\n❌ Ollama image analysis test failed.")
        return False
    
    print("\n✅ All tests passed! Ollama integration is ready.")
    print("\n📋 Next steps:")
    print("1. Start your Flask backend server")
    print("2. Upload a video with violence to test the full integration")
    print("3. Check that the violence description modal appears")
    
    return True

if __name__ == "__main__":
    main()
